"""
Professional VectorBT Backtester for Big Trend Strategy
=======================================================

Advanced VectorBT backtesting system for your big trend capture strategy:
- 200-300 point stop losses
- 800-3000+ point targets
- Trailing only after 1:4 R:R hit
- Max 3 trades/day, 3 SLs/day
- Comprehensive performance metrics
"""

import vectorbt as vbt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Import our indicator system
from enhanced_indicator import compute_enhanced_indicators, generate_enhanced_signals
from big_trend_system import BigTrendTradingSystem

class BigTrendVectorBTBacktester:
    """Professional VectorBT backtester for big trend strategy"""

    def __init__(self, initial_capital: float = 100000):
        """Initialize backtester"""

        self.initial_capital = initial_capital

        # Big Trend Strategy Settings
        self.strategy_settings = {
            # SIGNAL QUALITY
            'min_confluence': 4,           # Minimum 4 confluence signals
            'min_rr': 4.0,                # Minimum 1:4 R:R before trailing
            'target_rr': 5.0,             # Target 1:5 R:R
            'big_trend_rr': 15.0,         # Big trend target 1:15

            # STOP LOSS CONTROL (200-300 points)
            'max_sl_points': 300,         # Maximum 300 point stop loss
            'min_sl_points': 200,         # Minimum 200 point stop loss
            'atr_sl_multiplier': 1.5,     # Conservative ATR multiplier

            # DAILY LIMITS
            'max_trades_per_day': 3,      # Max 3 trades per day
            'max_sl_per_day': 3,          # Max 3 stop losses per day

            # TRAILING SETTINGS
            'start_trailing_at_rr': 4.0,  # Start trailing only after 1:4
            'trailing_step': 200,         # Trail in 200 point steps

            # RISK MANAGEMENT
            'base_risk': 0.015,           # 1.5% base risk per trade
            'max_risk': 0.025,            # 2.5% max risk for ultra signals

            # TRADING COSTS
            'fees': 0.0005,               # 0.05% fees (Delta Exchange)
            'slippage': 0.001,            # 0.1% slippage
        }

        self.results = {}

    def prepare_data(self, df_3m: pd.DataFrame, df_15m: pd.DataFrame, df_1h: pd.DataFrame):
        """Prepare multi-timeframe data for backtesting"""

        print("🔄 Preparing multi-timeframe data...")

        # Compute indicators for all timeframes
        df_3m_ind = compute_enhanced_indicators(df_3m, settings=self.strategy_settings)
        df_15m_ind = compute_enhanced_indicators(df_15m, settings=self.strategy_settings)
        df_1h_ind = compute_enhanced_indicators(df_1h, settings=self.strategy_settings)

        print(f"✅ Indicators computed:")
        print(f"   3m: {len(df_3m_ind)} candles")
        print(f"   15m: {len(df_15m_ind)} candles")
        print(f"   1h: {len(df_1h_ind)} candles")

        return df_3m_ind, df_15m_ind, df_1h_ind

    def generate_signals(self, df_3m_ind: pd.DataFrame, df_15m_ind: pd.DataFrame, df_1h_ind: pd.DataFrame):
        """Generate trading signals for backtesting"""

        print("🎯 Generating big trend signals...")

        signals_data = []
        daily_trades = {}
        daily_sls = {}

        # Process each 3m candle
        for i in range(max(50, len(df_3m_ind) - 1000), len(df_3m_ind)):  # Start from recent data

            current_date = df_3m_ind.index[i].strftime('%Y-%m-%d')

            # Initialize daily counters
            if current_date not in daily_trades:
                daily_trades[current_date] = 0
                daily_sls[current_date] = 0

            # Check daily limits
            if (daily_trades[current_date] >= self.strategy_settings['max_trades_per_day'] or
                daily_sls[current_date] >= self.strategy_settings['max_sl_per_day']):
                continue

            # Get aligned data for signal generation
            current_3m = df_3m_ind.iloc[i:i+1]

            # Find corresponding 15m and 1h data
            current_time = df_3m_ind.index[i]

            # Get latest 15m data up to current time
            df_15m_aligned = df_15m_ind[df_15m_ind.index <= current_time]
            if len(df_15m_aligned) == 0:
                continue
            current_15m = df_15m_aligned.iloc[-1:]

            # Get latest 1h data up to current time
            df_1h_aligned = df_1h_ind[df_1h_ind.index <= current_time]
            if len(df_1h_aligned) == 0:
                continue
            current_1h = df_1h_aligned.iloc[-1:]

            # Generate signal
            try:
                signal = generate_enhanced_signals(
                    current_3m, current_15m, current_1h,
                    self.strategy_settings
                )

                if signal and signal.confluence_count >= self.strategy_settings['min_confluence']:

                    # Calculate position size based on risk
                    risk_amount = self.initial_capital * signal.risk_allocation
                    sl_points = abs(signal.entry_price - signal.stop_loss)
                    position_size = risk_amount / sl_points if sl_points > 0 else 0

                    signals_data.append({
                        'timestamp': current_time,
                        'signal': signal.signal,
                        'entry_price': signal.entry_price,
                        'stop_loss': signal.stop_loss,
                        'take_profit': signal.take_profit,
                        'sl_points': sl_points,
                        'tp_points': abs(signal.take_profit - signal.entry_price),
                        'risk_reward': signal.risk_reward_ratio,
                        'confluence': signal.confluence_count,
                        'strength': signal.strength.name,
                        'position_size': position_size,
                        'risk_allocation': signal.risk_allocation
                    })

                    daily_trades[current_date] += 1

                    if i % 1000 == 0:
                        print(f"   Processed {i}/{len(df_3m_ind)} candles, found {len(signals_data)} signals")

            except Exception as e:
                continue

        signals_df = pd.DataFrame(signals_data)

        if len(signals_df) > 0:
            signals_df.set_index('timestamp', inplace=True)
            print(f"✅ Generated {len(signals_df)} big trend signals")
            print(f"   Signal distribution:")
            print(f"   BUY: {len(signals_df[signals_df['signal'] == 'BUY'])}")
            print(f"   SELL: {len(signals_df[signals_df['signal'] == 'SELL'])}")
            print(f"   Average R:R: {signals_df['risk_reward'].mean():.2f}")
        else:
            print("❌ No signals generated")

        return signals_df

    def create_vectorbt_signals(self, df_3m: pd.DataFrame, signals_df: pd.DataFrame):
        """Convert signals to VectorBT format"""

        if len(signals_df) == 0:
            print("❌ No signals to convert")
            return None, None, None, None

        print("🔄 Converting signals to VectorBT format...")

        # Create boolean arrays for entries and exits
        long_entries = pd.Series(False, index=df_3m.index)
        short_entries = pd.Series(False, index=df_3m.index)
        long_exits = pd.Series(False, index=df_3m.index)
        short_exits = pd.Series(False, index=df_3m.index)

        # Position sizes
        long_sizes = pd.Series(0.0, index=df_3m.index)
        short_sizes = pd.Series(0.0, index=df_3m.index)

        # Stop losses and take profits
        stop_losses = pd.Series(np.nan, index=df_3m.index)
        take_profits = pd.Series(np.nan, index=df_3m.index)

        for idx, signal in signals_df.iterrows():
            if idx in df_3m.index:
                if signal['signal'] == 'BUY':
                    long_entries.loc[idx] = True
                    long_sizes.loc[idx] = signal['position_size']
                    stop_losses.loc[idx] = signal['stop_loss']
                    take_profits.loc[idx] = signal['take_profit']
                else:  # SELL
                    short_entries.loc[idx] = True
                    short_sizes.loc[idx] = signal['position_size']
                    stop_losses.loc[idx] = signal['stop_loss']
                    take_profits.loc[idx] = signal['take_profit']

        print(f"✅ VectorBT signals created:")
        print(f"   Long entries: {long_entries.sum()}")
        print(f"   Short entries: {short_entries.sum()}")

        return long_entries, short_entries, long_sizes, short_sizes, stop_losses, take_profits

    def run_vectorbt_backtest(self, df_3m: pd.DataFrame, signals_df: pd.DataFrame):
        """Run comprehensive VectorBT backtest with big trend features"""

        if len(signals_df) == 0:
            print("❌ No signals to backtest")
            return None

        print("🚀 Running VectorBT backtest...")

        # Get VectorBT signals
        long_entries, short_entries, long_sizes, short_sizes, stop_losses, take_profits = self.create_vectorbt_signals(df_3m, signals_df)

        if long_entries is None:
            return None

        # Create VectorBT portfolio with advanced features
        pf = vbt.Portfolio.from_signals(
            close=df_3m['close'],
            entries=long_entries | short_entries,
            exits=False,  # We'll handle exits with stop loss and take profit
            size=long_sizes + short_sizes,
            direction=np.where(long_entries, 1, -1),  # 1 for long, -1 for short
            init_cash=self.initial_capital,
            fees=self.strategy_settings['fees'],
            slippage=self.strategy_settings['slippage'],
            sl_stop=stop_losses,  # Stop loss levels
            tp_stop=take_profits,  # Take profit levels
            upon_stop_exit='Close',  # Close position when stop hit
            freq='3min'
        )

        print("✅ VectorBT backtest completed!")

        return pf

    def analyze_results(self, pf, signals_df: pd.DataFrame):
        """Comprehensive analysis of backtest results"""

        if pf is None:
            return {}

        print("\n📊 ANALYZING BIG TREND BACKTEST RESULTS")
        print("=" * 60)

        # Basic performance metrics
        total_return = pf.total_return()
        total_trades = pf.trades.count()
        win_rate = pf.trades.win_rate()
        profit_factor = pf.trades.profit_factor()
        max_drawdown = pf.max_drawdown()
        sharpe_ratio = pf.sharpe_ratio()

        # Advanced metrics
        avg_win = pf.trades.winning.pnl.mean() if len(pf.trades.winning.pnl) > 0 else 0
        avg_loss = pf.trades.losing.pnl.mean() if len(pf.trades.losing.pnl) > 0 else 0
        max_win = pf.trades.winning.pnl.max() if len(pf.trades.winning.pnl) > 0 else 0
        max_loss = pf.trades.losing.pnl.min() if len(pf.trades.losing.pnl) > 0 else 0

        # Big trend specific metrics
        big_winners = len(pf.trades.winning.pnl[pf.trades.winning.pnl > 8000])  # 800+ point moves
        huge_winners = len(pf.trades.winning.pnl[pf.trades.winning.pnl > 20000])  # 2000+ point moves

        # Calculate expectancy
        expectancy = (win_rate * avg_win) + ((1 - win_rate) * avg_loss)

        # Signal strength analysis
        strength_performance = {}
        for strength in signals_df['strength'].unique():
            strength_signals = signals_df[signals_df['strength'] == strength]
            strength_performance[strength] = {
                'count': len(strength_signals),
                'avg_rr': strength_signals['risk_reward'].mean(),
                'avg_confluence': strength_signals['confluence'].mean()
            }

        results = {
            # PERFORMANCE METRICS
            'total_return': total_return,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'expectancy': expectancy,

            # TRADE ANALYSIS
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'max_win': max_win,
            'max_loss': max_loss,
            'big_winners': big_winners,
            'huge_winners': huge_winners,

            # SIGNAL ANALYSIS
            'total_signals': len(signals_df),
            'avg_risk_reward': signals_df['risk_reward'].mean(),
            'avg_confluence': signals_df['confluence'].mean(),
            'strength_performance': strength_performance,

            # PORTFOLIO OBJECT
            'portfolio': pf
        }

        self.results = results
        return results

    def print_detailed_results(self):
        """Print comprehensive backtest results"""

        if not self.results:
            print("❌ No results to display")
            return

        r = self.results

        print(f"\n🎯 BIG TREND STRATEGY BACKTEST RESULTS")
        print(f"=" * 60)

        print(f"\n📈 PERFORMANCE OVERVIEW:")
        print(f"   Initial Capital: ${self.initial_capital:,.0f}")
        print(f"   Final Value: ${self.initial_capital * (1 + r['total_return']):,.0f}")
        print(f"   Total Return: {r['total_return']:.2%}")
        print(f"   Max Drawdown: {r['max_drawdown']:.2%}")
        print(f"   Sharpe Ratio: {r['sharpe_ratio']:.2f}")

        print(f"\n🎲 TRADE STATISTICS:")
        print(f"   Total Trades: {r['total_trades']}")
        print(f"   Win Rate: {r['win_rate']:.2%}")
        print(f"   Profit Factor: {r['profit_factor']:.2f}")
        print(f"   Expectancy: ${r['expectancy']:,.0f}")

        print(f"\n💰 TRADE ANALYSIS:")
        print(f"   Average Win: ${r['avg_win']:,.0f}")
        print(f"   Average Loss: ${r['avg_loss']:,.0f}")
        print(f"   Best Trade: ${r['max_win']:,.0f}")
        print(f"   Worst Trade: ${r['max_loss']:,.0f}")

        print(f"\n🚀 BIG TREND CAPTURES:")
        print(f"   Big Winners (800+ pts): {r['big_winners']}")
        print(f"   Huge Winners (2000+ pts): {r['huge_winners']}")
        print(f"   Big Winner Rate: {r['big_winners']/r['total_trades']:.1%}")

        print(f"\n🎯 SIGNAL QUALITY:")
        print(f"   Total Signals: {r['total_signals']}")
        print(f"   Average R:R: {r['avg_risk_reward']:.2f}")
        print(f"   Average Confluence: {r['avg_confluence']:.1f}")

        print(f"\n📊 SIGNAL STRENGTH BREAKDOWN:")
        for strength, stats in r['strength_performance'].items():
            print(f"   {strength}: {stats['count']} signals, R:R {stats['avg_rr']:.1f}, Confluence {stats['avg_confluence']:.1f}")

        # Performance rating
        if r['total_return'] > 0.5:  # 50%+ return
            rating = "🏆 EXCELLENT"
        elif r['total_return'] > 0.2:  # 20%+ return
            rating = "✅ GOOD"
        elif r['total_return'] > 0:  # Positive return
            rating = "📈 PROFITABLE"
        else:
            rating = "📉 NEEDS IMPROVEMENT"

        print(f"\n{rating} - Strategy Performance Rating")
        print(f"=" * 60)

    def run_full_backtest(self, df_3m: pd.DataFrame, df_15m: pd.DataFrame, df_1h: pd.DataFrame):
        """Run complete backtest pipeline"""

        print(f"\n🚀 STARTING BIG TREND STRATEGY BACKTEST")
        print(f"=" * 60)
        print(f"Initial Capital: ${self.initial_capital:,.0f}")
        print(f"Data Period: {df_3m.index[0]} to {df_3m.index[-1]}")
        print(f"Total 3m Candles: {len(df_3m):,}")

        # Step 1: Prepare data
        df_3m_ind, df_15m_ind, df_1h_ind = self.prepare_data(df_3m, df_15m, df_1h)

        # Step 2: Generate signals
        signals_df = self.generate_signals(df_3m_ind, df_15m_ind, df_1h_ind)

        if len(signals_df) == 0:
            print("❌ No signals generated - check strategy parameters")
            return None

        # Step 3: Run VectorBT backtest
        pf = self.run_vectorbt_backtest(df_3m_ind, signals_df)

        # Step 4: Analyze results
        results = self.analyze_results(pf, signals_df)

        # Step 5: Print detailed results
        self.print_detailed_results()

        return results

# Convenience function for easy backtesting
def run_big_trend_backtest(df_3m: pd.DataFrame, df_15m: pd.DataFrame, df_1h: pd.DataFrame, initial_capital: float = 100000):
    """
    Easy-to-use function for running big trend backtest

    Args:
        df_3m: 3-minute OHLCV data
        df_15m: 15-minute OHLCV data
        df_1h: 1-hour OHLCV data
        initial_capital: Starting capital (default $100,000)

    Returns:
        Dictionary with comprehensive backtest results
    """

    backtester = BigTrendVectorBTBacktester(initial_capital)
    results = backtester.run_full_backtest(df_3m, df_15m, df_1h)

    return results
