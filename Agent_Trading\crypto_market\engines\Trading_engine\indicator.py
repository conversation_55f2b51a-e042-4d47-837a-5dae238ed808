# Try to import TA-Lib, fallback to pandas-ta if not available
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("⚠️ TA-Lib not found, using pandas-ta as fallback")
    try:
        import pandas_ta as ta
        PANDAS_TA_AVAILABLE = True
    except ImportError:
        PANDAS_TA_AVAILABLE = False
        print("❌ Neither TA-Lib nor pandas-ta available. Please install one of them:")
        print("   conda install -c conda-forge ta-lib")
        print("   OR")
        print("   pip install pandas-ta")
import pandas as pd
import numpy as np



def compute_indicators(
    df: pd.DataFrame,
    indicators: list = None,
    settings: dict = None
) -> pd.DataFrame:
    df = df.sort_values('timestamp').copy()

    if indicators is None:
        indicators = [
            'macd', 'rsi', 'adx', 'di', 'sma', 'bbands',
            'atr', 'avg_volume', 'obv', 'ema', 'hma', 'supertrend'
        ]

    if settings is None:
        settings = {
            'sma': [50, 200],
            'ema': [9],
            'rsi': 14,
            'rsi_buy_threshold': 30,
            'rsi_sell_threshold': 70,
            'adx': 14,
            'bbands': 20,
            'atr': 14,
            'hma': [9],
            'avg_volume': 20,
            'supertrend': {'period': 10, 'multiplier': 3}
        }

    if 'macd' in indicators:
        df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(df['close'])

    if 'rsi' in indicators:
        df['rsi'] = talib.RSI(df['close'], timeperiod=settings.get('rsi', 14))

    if 'adx' in indicators or 'di' in indicators:
        adx_period = settings.get('adx', 14)
        if 'adx' in indicators:
            df['adx'] = talib.ADX(df['high'], df['low'], df['close'], timeperiod=adx_period)
        if 'di' in indicators:
            df['plus_di'] = talib.PLUS_DI(df['high'], df['low'], df['close'], timeperiod=adx_period)
            df['minus_di'] = talib.MINUS_DI(df['high'], df['low'], df['close'], timeperiod=adx_period)

    if 'sma' in indicators:
        for period in settings.get('sma', [50, 200]):
            df[f'sma_{period}'] = talib.SMA(df['close'], timeperiod=period)

    if 'ema' in indicators:
        for period in settings.get('ema', [9]):
            df[f'ema_{period}'] = talib.EMA(df['close'], timeperiod=period)

    if 'bbands' in indicators:
        bb_period = settings.get('bbands', 20)
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(df['close'], timeperiod=bb_period)

    if 'atr' in indicators:
        atr_period = settings.get('atr', 14)
        df['atr'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=atr_period)

    if 'obv' in indicators:
        df['obv'] = talib.OBV(df['close'], df['volume'])

    if 'avg_volume' in indicators:
        vol_period = settings.get('avg_volume', 20)
        df['avg_volume'] = df['volume'].rolling(window=vol_period).mean()

    if 'hma' in indicators:
        def WMA(series, period):
            weights = np.arange(1, period + 1)
            return series.rolling(period).apply(lambda prices: np.dot(prices, weights) / weights.sum(), raw=True)

        def HMA(series, period):
            half = int(period / 2)
            sqrt = int(np.sqrt(period))
            wma_half = WMA(series, half)
            wma_full = WMA(series, period)
            raw_hma = 2 * wma_half - wma_full
            return WMA(raw_hma, sqrt)

        for period in settings.get('hma', [9]):
            df[f'hma_{period}'] = HMA(df['close'], period)

    if 'supertrend' in indicators:
        period = settings['supertrend'].get('period', 10)
        multiplier = settings['supertrend'].get('multiplier', 3)

        atr = talib.ATR(df['high'], df['low'], df['close'], timeperiod=period)
        hl2 = (df['high'] + df['low']) / 2
        upperband = hl2 + (multiplier * atr)
        lowerband = hl2 - (multiplier * atr)

        supertrend = pd.Series(index=df.index, dtype=bool)
        direction = pd.Series(index=df.index, dtype=int)

        final_upperband = upperband.copy()
        final_lowerband = lowerband.copy()

        for i in range(1, len(df)):
            if df['close'][i - 1] > final_upperband[i - 1]:
                final_upperband[i] = min(upperband[i], final_upperband[i - 1])
            else:
                final_upperband[i] = upperband[i]

            if df['close'][i - 1] < final_lowerband[i - 1]:
                final_lowerband[i] = max(lowerband[i], final_lowerband[i - 1])
            else:
                final_lowerband[i] = lowerband[i]

            if df['close'][i] > final_upperband[i - 1]:
                supertrend[i] = True
            elif df['close'][i] < final_lowerband[i - 1]:
                supertrend[i] = False
            else:
                supertrend[i] = supertrend[i - 1] if i > 0 else True

            direction[i] = 1 if supertrend[i] else -1

        df['supertrend'] = direction

    df = df.dropna().reset_index(drop=True)
    return df


def generate_trade_signal(df_3min, df_15min, df_1hr, atr_sl_mult=1.5, atr_tp_mult=3.0):
    df_3min = compute_indicators(df_3min)
    df_15min = compute_indicators(df_15min)
    df_1hr = compute_indicators(df_1hr)

    latest_3 = df_3min.iloc[-1]
    prev_3 = df_3min.iloc[-2] if len(df_3min) > 1 else latest_3

    latest_15 = df_15min.iloc[-1]
    prev_15 = df_15min.iloc[-2] if len(df_15min) > 1 else latest_15

    latest_1h = df_1hr.iloc[-1]
    prev_1h = df_1hr.iloc[-2] if len(df_1hr) > 1 else latest_1h

    buy_score = 0
    sell_score = 0

    # --- 3-MIN Signal Logic ---
    if prev_3['macd'] < prev_3['macd_signal'] and latest_3['macd'] > latest_3['macd_signal']:
        buy_score += 1.5
    elif prev_3['macd'] > prev_3['macd_signal'] and latest_3['macd'] < latest_3['macd_signal']:
        sell_score += 1.5

    if latest_3['rsi'] < 30:
        buy_score += 1.0
    elif latest_3['rsi'] > 70:
        sell_score += 1.0

    if latest_3['close'] > latest_3['ema_9']:
        buy_score += 0.5
    else:
        sell_score += 0.5

    if latest_3['close'] > latest_3['bb_middle']:
        buy_score += 0.5
    else:
        sell_score += 0.5

    if latest_3['obv'] > prev_3['obv'] and latest_3['volume'] >= latest_3['avg_volume']:
        buy_score += 0.5
    elif latest_3['obv'] < prev_3['obv'] and latest_3['volume'] >= latest_3['avg_volume']:
        sell_score += 0.5

    if latest_3['hma_9'] > prev_3['hma_9']:
        buy_score += 0.5
    else:
        sell_score += 0.5

    if latest_3['atr'] > df_3min['atr'].quantile(0.5):
        buy_score += 0.5
        sell_score += 0.5

    # --- 15-MIN Confirmation Logic ---
    if prev_15['macd'] < prev_15['macd_signal'] and latest_15['macd'] > latest_15['macd_signal']:
        buy_score += 1.0
    elif prev_15['macd'] > prev_15['macd_signal'] and latest_15['macd'] < latest_15['macd_signal']:
        sell_score += 1.0

    if latest_15['adx'] > 25:
        if latest_15['plus_di'] > latest_15['minus_di']:
            buy_score += 0.5
        else:
            sell_score += 0.5

    if latest_15['hma_9'] > prev_15['hma_9']:
        buy_score += 0.5
    else:
        sell_score += 0.5

    # --- 1-HOUR Trend Filter ---
    if latest_1h['sma_50'] > latest_1h['sma_200']:
        buy_score += 1.0
    elif latest_1h['sma_50'] < latest_1h['sma_200']:
        sell_score += 1.0

    if latest_1h['hma_9'] > prev_1h['hma_9']:
        buy_score += 0.5
    else:
        sell_score += 0.5

    # --- Final Decision ---
    score_diff = abs(buy_score - sell_score)
    signal = 'hold'
    if buy_score >= 4.0 and buy_score > sell_score and score_diff >= 1.0:
        signal = 'buy'
    elif sell_score >= 4.0 and sell_score > buy_score and score_diff >= 1.0:
        signal = 'sell'

    # --- ATR-based SL & TP ---
    atr = latest_3['atr']
    entry_price = latest_3['close']
    stop_loss = take_profit = None

    if signal == 'buy':
        stop_loss = entry_price - atr * atr_sl_mult
        take_profit = entry_price + atr * atr_tp_mult
    elif signal == 'sell':
        stop_loss = entry_price + atr * atr_sl_mult
        take_profit = entry_price - atr * atr_tp_mult

    return {
        'signal': signal,
        'entry_price': entry_price,
        'stop_loss': stop_loss,
        'take_profit': take_profit,
        'buy_score': round(buy_score, 2),
        'sell_score': round(sell_score, 2)
    }
