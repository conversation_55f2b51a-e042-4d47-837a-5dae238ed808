"""
Enhanced Trading System Usage Example
====================================

Example implementation of the enhanced indicator system with:
- Professional signal generation
- Trade logging and metrics
- Dynamic risk management
- Trailing stop implementation
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add project paths
sys.path.append(str(Path(__file__).parent))

from enhanced_indicator import (
    compute_enhanced_indicators,
    generate_enhanced_signals,
    metrics_tracker,
    SignalStrength,
    TrailingMethod
)

class EnhancedTradingSystem:
    """Professional trading system with enhanced features"""
    
    def __init__(self, settings: dict = None):
        """Initialize trading system"""
        
        self.settings = settings or {
            # Signal generation settings
            'min_confluence': 3,
            'base_risk': 0.01,      # 1% base risk
            'max_risk': 0.025,      # 2.5% max risk
            'min_rr': 2.0,          # Minimum 2:1 R:R
            'ultra_high_rr': 6.0,   # Ultra high R:R target
            'atr_multiplier': 2.0,
            'trailing_atr_mult': 1.5,
            
            # Indicator settings
            'sma': [50, 200],
            'ema': [9, 21],
            'rsi': 14,
            'adx': 14,
            'bbands': 20,
            'atr': 14,
            'hma': [9],
            'avg_volume': 20,
            'supertrend': {'period': 10, 'multiplier': 3},
            'volatility_lookback': 20
        }
        
        self.active_trades = []
        self.trade_history = []
        
    def process_market_data(self, df_3m: pd.DataFrame, df_15m: pd.DataFrame, df_1h: pd.DataFrame):
        """Process market data and generate signals"""
        
        # Compute indicators for all timeframes
        df_3m_indicators = compute_enhanced_indicators(df_3m, settings=self.settings)
        df_15m_indicators = compute_enhanced_indicators(df_15m, settings=self.settings)
        df_1h_indicators = compute_enhanced_indicators(df_1h, settings=self.settings)
        
        # Generate enhanced signals
        signal = generate_enhanced_signals(
            df_3m_indicators,
            df_15m_indicators,
            df_1h_indicators,
            settings=self.settings
        )
        
        if signal:
            self.handle_new_signal(signal)
            
        # Update trailing stops for active trades
        self.update_trailing_stops(df_3m_indicators.iloc[-1])
        
        return signal
    
    def handle_new_signal(self, signal):
        """Handle new trading signal"""
        
        print(f"\n🚨 NEW SIGNAL GENERATED 🚨")
        print(f"Direction: {signal.signal}")
        print(f"Strength: {signal.strength.name}")
        print(f"Entry Price: ${signal.entry_price:,.2f}")
        print(f"Stop Loss: ${signal.stop_loss:,.2f}")
        print(f"Take Profit: ${signal.take_profit:,.2f}")
        print(f"Risk/Reward: {signal.risk_reward_ratio:.2f}")
        print(f"Risk Allocation: {signal.risk_allocation:.2%}")
        print(f"Confidence: {signal.confidence:.1f}%")
        print(f"Trailing Method: {signal.trailing_method.value}")
        print(f"Confluence Count: {signal.confluence_count}")
        print(f"Timeframe Alignment: {signal.timeframe_alignment}")
        
        # Add to active trades (in real system, this would place the trade)
        trade = {
            'signal': signal,
            'entry_time': signal.timestamp,
            'status': 'active',
            'current_stop': signal.stop_loss,
            'trailing_method': signal.trailing_method
        }
        
        self.active_trades.append(trade)
        
    def update_trailing_stops(self, current_data):
        """Update trailing stops for active trades"""
        
        current_price = current_data['close']
        atr = current_data.get('atr', current_price * 0.02)
        
        for trade in self.active_trades:
            if trade['status'] != 'active':
                continue
                
            signal = trade['signal']
            trailing_method = trade['trailing_method']
            
            new_stop = self.calculate_trailing_stop(
                signal, current_price, atr, trailing_method, trade['current_stop']
            )
            
            # Update stop if it's better
            if signal.signal == "BUY" and new_stop > trade['current_stop']:
                trade['current_stop'] = new_stop
                print(f"📈 Trailing stop updated for BUY: ${new_stop:,.2f}")
                
            elif signal.signal == "SELL" and new_stop < trade['current_stop']:
                trade['current_stop'] = new_stop
                print(f"📉 Trailing stop updated for SELL: ${new_stop:,.2f}")
            
            # Check if stop hit
            if ((signal.signal == "BUY" and current_price <= trade['current_stop']) or
                (signal.signal == "SELL" and current_price >= trade['current_stop'])):
                
                self.close_trade(trade, current_price, "Stop Loss Hit")
                
            # Check if take profit hit
            elif ((signal.signal == "BUY" and current_price >= signal.take_profit) or
                  (signal.signal == "SELL" and current_price <= signal.take_profit)):
                
                self.close_trade(trade, current_price, "Take Profit Hit")
    
    def calculate_trailing_stop(self, signal, current_price, atr, trailing_method, current_stop):
        """Calculate new trailing stop based on method"""
        
        if trailing_method == TrailingMethod.ATR_BASED:
            # ATR-based trailing
            atr_mult = self.settings.get('trailing_atr_mult', 1.5)
            
            if signal.signal == "BUY":
                return current_price - (atr * atr_mult)
            else:
                return current_price + (atr * atr_mult)
                
        elif trailing_method == TrailingMethod.STRUCTURE_BASED:
            # Structure-based trailing (simplified)
            # In real implementation, this would use swing highs/lows
            if signal.signal == "BUY":
                return max(current_stop, current_price * 0.98)  # 2% trailing
            else:
                return min(current_stop, current_price * 1.02)  # 2% trailing
                
        elif trailing_method == TrailingMethod.DYNAMIC:
            # Dynamic trailing based on volatility
            vol_mult = 1.0 + (atr / current_price)  # Adjust for volatility
            
            if signal.signal == "BUY":
                return current_price - (atr * 1.5 * vol_mult)
            else:
                return current_price + (atr * 1.5 * vol_mult)
        
        return current_stop
    
    def close_trade(self, trade, exit_price, reason):
        """Close a trade and calculate P&L"""
        
        signal = trade['signal']
        entry_price = signal.entry_price
        
        if signal.signal == "BUY":
            pnl_points = exit_price - entry_price
        else:
            pnl_points = entry_price - exit_price
            
        pnl_percentage = (pnl_points / entry_price) * 100
        
        trade['status'] = 'closed'
        trade['exit_time'] = datetime.now()
        trade['exit_price'] = exit_price
        trade['pnl_points'] = pnl_points
        trade['pnl_percentage'] = pnl_percentage
        trade['close_reason'] = reason
        
        self.trade_history.append(trade)
        
        print(f"\n💰 TRADE CLOSED 💰")
        print(f"Direction: {signal.signal}")
        print(f"Entry: ${entry_price:,.2f}")
        print(f"Exit: ${exit_price:,.2f}")
        print(f"P&L: {pnl_points:+.2f} points ({pnl_percentage:+.2f}%)")
        print(f"Reason: {reason}")
        
        # Log false signal if it was a loss
        if pnl_points < 0:
            metrics_tracker.log_false_signal(signal, f"Loss: {reason}")
    
    def get_performance_summary(self):
        """Get trading performance summary"""
        
        if not self.trade_history:
            return "No completed trades yet."
        
        total_trades = len(self.trade_history)
        winning_trades = len([t for t in self.trade_history if t['pnl_points'] > 0])
        losing_trades = total_trades - winning_trades
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        total_pnl = sum(t['pnl_points'] for t in self.trade_history)
        avg_win = np.mean([t['pnl_points'] for t in self.trade_history if t['pnl_points'] > 0]) if winning_trades > 0 else 0
        avg_loss = np.mean([t['pnl_points'] for t in self.trade_history if t['pnl_points'] < 0]) if losing_trades > 0 else 0
        
        profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if losing_trades > 0 and avg_loss != 0 else float('inf')
        
        expectancy = (win_rate * avg_win) + ((1 - win_rate) * avg_loss)
        
        summary = f"""
📊 TRADING PERFORMANCE SUMMARY 📊
================================
Total Trades: {total_trades}
Winning Trades: {winning_trades}
Losing Trades: {losing_trades}
Win Rate: {win_rate:.2%}
Total P&L: {total_pnl:+.2f} points
Average Win: {avg_win:.2f} points
Average Loss: {avg_loss:.2f} points
Profit Factor: {profit_factor:.2f}
Expectancy: {expectancy:.2f} points
================================
        """
        
        return summary

# Example usage
def example_usage():
    """Example of how to use the enhanced trading system"""
    
    # Initialize trading system
    trading_system = EnhancedTradingSystem()
    
    # In real implementation, you would get this data from your database
    # For demo, create sample data
    dates = pd.date_range(start='2024-01-01', periods=1000, freq='3min')
    
    # Sample 3m data
    df_3m = pd.DataFrame({
        'timestamp': dates,
        'open': 50000 + np.random.randn(1000) * 100,
        'high': 50100 + np.random.randn(1000) * 100,
        'low': 49900 + np.random.randn(1000) * 100,
        'close': 50000 + np.random.randn(1000) * 100,
        'volume': 1000 + np.random.randn(1000) * 100
    })
    
    # Sample 15m and 1h data (simplified)
    df_15m = df_3m.iloc[::5].copy()  # Every 5th row
    df_1h = df_3m.iloc[::20].copy()  # Every 20th row
    
    print("🚀 Enhanced Trading System Example")
    print("=" * 50)
    
    # Process market data
    signal = trading_system.process_market_data(df_3m, df_15m, df_1h)
    
    if signal:
        print("\n✅ Signal generated successfully!")
    else:
        print("\n⏳ No signal generated (insufficient confluence)")
    
    # Get metrics
    performance_stats = metrics_tracker.get_performance_stats()
    print(f"\n📈 Current Metrics: {performance_stats}")
    
    print("\n🎯 Enhanced Trading System Ready!")

if __name__ == "__main__":
    example_usage()
