"""
Big Trend Trading System
========================

Specialized system for capturing BTC big trends (800-3000+ points) with:
- Max 2-3 trades per day
- Max 3 stop losses per day  
- Small stop losses (200-300 points)
- Big targets (1:4 to 1:15 R:R)
- Trailing only after minimum target hit
- Intraday + Swing trading combined
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
from dataclasses import dataclass
from enum import Enum

from enhanced_indicator import (
    compute_enhanced_indicators,
    generate_enhanced_signals,
    SignalStrength,
    TrailingMethod
)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DailyLimits:
    """Track daily trading limits"""
    date: str
    trades_taken: int = 0
    stop_losses_hit: int = 0
    max_trades: int = 3
    max_stop_losses: int = 3
    
    def can_take_trade(self) -> bool:
        """Check if we can take another trade today"""
        return self.trades_taken < self.max_trades and self.stop_losses_hit < self.max_stop_losses
    
    def can_risk_stop_loss(self) -> bool:
        """Check if we can risk another stop loss today"""
        return self.stop_losses_hit < self.max_stop_losses

class BigTrendTradingSystem:
    """Professional Big Trend Trading System"""
    
    def __init__(self):
        """Initialize the big trend trading system"""
        
        # Big Trend Capture Settings
        self.settings = {
            # SIGNAL QUALITY (Higher standards for big moves)
            'min_confluence': 4,           # Minimum 4 confluence signals
            'min_rr': 4.0,                # Minimum 1:4 R:R before trailing
            'target_rr': 5.0,             # Target 1:5 R:R
            'big_trend_rr': 15.0,         # Big trend target 1:15 (3000 points)
            
            # STOP LOSS CONTROL (200-300 points max)
            'max_sl_points': 300,         # Maximum 300 point stop loss
            'min_sl_points': 200,         # Minimum 200 point stop loss
            'atr_sl_multiplier': 1.5,     # Conservative ATR multiplier
            
            # DAILY LIMITS
            'max_trades_per_day': 3,      # Max 3 trades per day
            'max_sl_per_day': 3,          # Max 3 stop losses per day
            
            # TRAILING SETTINGS (Only after minimum target)
            'start_trailing_at_rr': 4.0,  # Start trailing only after 1:4 hit
            'trailing_step': 200,         # Trail in 200 point steps
            'trailing_atr_mult': 1.0,     # Tight trailing after target
            
            # RISK MANAGEMENT
            'base_risk': 0.015,           # 1.5% base risk
            'max_risk': 0.025,            # 2.5% max risk for ultra signals
        }
        
        self.active_trades = []
        self.daily_limits = {}
        self.trade_history = []
        
    def get_daily_limits(self, date_str: str) -> DailyLimits:
        """Get or create daily limits for a date"""
        if date_str not in self.daily_limits:
            self.daily_limits[date_str] = DailyLimits(date=date_str)
        return self.daily_limits[date_str]
    
    def process_market_data(self, df_3m: pd.DataFrame, df_15m: pd.DataFrame, df_1h: pd.DataFrame):
        """Process market data for big trend signals"""
        
        # Get current date for daily limits
        current_date = datetime.now().strftime('%Y-%m-%d')
        daily_limits = self.get_daily_limits(current_date)
        
        # Check daily limits before generating signals
        if not daily_limits.can_take_trade():
            logger.info(f"Daily limits reached: {daily_limits.trades_taken}/{daily_limits.max_trades} trades, "
                       f"{daily_limits.stop_losses_hit}/{daily_limits.max_stop_losses} SLs")
            return None
        
        # Compute indicators
        df_3m_ind = compute_enhanced_indicators(df_3m, settings=self.settings)
        df_15m_ind = compute_enhanced_indicators(df_15m, settings=self.settings)
        df_1h_ind = compute_enhanced_indicators(df_1h, settings=self.settings)
        
        # Generate signal with big trend settings
        signal = generate_enhanced_signals(df_3m_ind, df_15m_ind, df_1h_ind, self.settings)
        
        if signal:
            # Additional validation for big trend signals
            if self.validate_big_trend_signal(signal, daily_limits):
                self.execute_trade(signal, daily_limits)
                return signal
        
        # Update trailing stops for active trades
        self.update_trailing_stops(df_3m_ind.iloc[-1])
        
        return None
    
    def validate_big_trend_signal(self, signal, daily_limits: DailyLimits) -> bool:
        """Validate signal meets big trend criteria"""
        
        # Check if we can risk another stop loss
        if not daily_limits.can_risk_stop_loss():
            logger.info("Cannot take trade - daily stop loss limit reached")
            return False
        
        # Ensure minimum R:R ratio
        if signal.risk_reward_ratio < self.settings['min_rr']:
            logger.info(f"Signal rejected - R:R {signal.risk_reward_ratio:.2f} below minimum {self.settings['min_rr']}")
            return False
        
        # Ensure stop loss is within limits
        entry_price = signal.entry_price
        sl_points = abs(entry_price - signal.stop_loss)
        
        if sl_points > self.settings['max_sl_points']:
            logger.info(f"Signal rejected - SL {sl_points:.0f} points exceeds max {self.settings['max_sl_points']}")
            return False
        
        # Require higher confluence for big trend trades
        if signal.confluence_count < self.settings['min_confluence']:
            logger.info(f"Signal rejected - confluence {signal.confluence_count} below minimum {self.settings['min_confluence']}")
            return False
        
        return True
    
    def execute_trade(self, signal, daily_limits: DailyLimits):
        """Execute a big trend trade"""
        
        # Calculate position size based on stop loss points
        entry_price = signal.entry_price
        sl_points = abs(entry_price - signal.stop_loss)
        tp_points = abs(signal.take_profit - entry_price)
        
        # Create trade record
        trade = {
            'signal': signal,
            'entry_time': datetime.now(),
            'entry_price': entry_price,
            'stop_loss': signal.stop_loss,
            'take_profit': signal.take_profit,
            'sl_points': sl_points,
            'tp_points': tp_points,
            'status': 'active',
            'trailing_started': False,
            'current_stop': signal.stop_loss,
            'max_favorable': entry_price,  # Track maximum favorable movement
            'trailing_method': signal.trailing_method
        }
        
        self.active_trades.append(trade)
        daily_limits.trades_taken += 1
        
        # Log trade execution
        logger.info(f"\n🚀 BIG TREND TRADE EXECUTED 🚀")
        logger.info(f"Direction: {signal.signal}")
        logger.info(f"Strength: {signal.strength.name}")
        logger.info(f"Entry: ${entry_price:,.2f}")
        logger.info(f"Stop Loss: ${signal.stop_loss:,.2f} ({sl_points:.0f} points)")
        logger.info(f"Take Profit: ${signal.take_profit:,.2f} ({tp_points:.0f} points)")
        logger.info(f"R:R Ratio: 1:{signal.risk_reward_ratio:.1f}")
        logger.info(f"Risk Allocation: {signal.risk_allocation:.2%}")
        logger.info(f"Confluence: {signal.confluence_count}")
        logger.info(f"Daily Trades: {daily_limits.trades_taken}/{daily_limits.max_trades}")
    
    def update_trailing_stops(self, current_data):
        """Update trailing stops - only after minimum target hit"""
        
        current_price = current_data['close']
        current_date = datetime.now().strftime('%Y-%m-%d')
        daily_limits = self.get_daily_limits(current_date)
        
        for trade in self.active_trades:
            if trade['status'] != 'active':
                continue
            
            signal = trade['signal']
            entry_price = trade['entry_price']
            
            # Update maximum favorable movement
            if signal.signal == "BUY":
                trade['max_favorable'] = max(trade['max_favorable'], current_price)
                current_profit_points = current_price - entry_price
            else:
                trade['max_favorable'] = min(trade['max_favorable'], current_price)
                current_profit_points = entry_price - current_price
            
            # Check if minimum R:R target hit (start trailing)
            min_target_points = trade['sl_points'] * self.settings['start_trailing_at_rr']
            
            if current_profit_points >= min_target_points and not trade['trailing_started']:
                trade['trailing_started'] = True
                logger.info(f"🎯 Minimum target hit! Starting trailing stop at {current_profit_points:.0f} points profit")
            
            # Update trailing stop only if trailing has started
            if trade['trailing_started']:
                new_stop = self.calculate_big_trend_trailing_stop(trade, current_price, current_data)
                
                # Update stop if it's better
                if signal.signal == "BUY" and new_stop > trade['current_stop']:
                    trade['current_stop'] = new_stop
                    logger.info(f"📈 Trailing stop updated: ${new_stop:,.2f} (Profit: {current_profit_points:.0f} points)")
                    
                elif signal.signal == "SELL" and new_stop < trade['current_stop']:
                    trade['current_stop'] = new_stop
                    logger.info(f"📉 Trailing stop updated: ${new_stop:,.2f} (Profit: {current_profit_points:.0f} points)")
            
            # Check stop loss hit
            if ((signal.signal == "BUY" and current_price <= trade['current_stop']) or
                (signal.signal == "SELL" and current_price >= trade['current_stop'])):
                
                self.close_trade(trade, current_price, "Trailing Stop Hit", daily_limits)
                
            # Check original take profit hit (for non-trailing exits)
            elif ((signal.signal == "BUY" and current_price >= trade['take_profit']) or
                  (signal.signal == "SELL" and current_price <= trade['take_profit'])):
                
                self.close_trade(trade, current_price, "Take Profit Hit", daily_limits)
    
    def calculate_big_trend_trailing_stop(self, trade, current_price, current_data):
        """Calculate trailing stop for big trend capture"""
        
        signal = trade['signal']
        atr = current_data.get('atr', current_price * 0.02)
        trailing_step = self.settings['trailing_step']
        
        # Use 200-point step trailing for big trends
        if signal.signal == "BUY":
            # Trail by 200-point steps below current price
            return current_price - trailing_step
        else:
            # Trail by 200-point steps above current price
            return current_price + trailing_step
    
    def close_trade(self, trade, exit_price, reason, daily_limits: DailyLimits):
        """Close a trade and update statistics"""
        
        signal = trade['signal']
        entry_price = trade['entry_price']
        
        if signal.signal == "BUY":
            pnl_points = exit_price - entry_price
        else:
            pnl_points = entry_price - exit_price
        
        pnl_percentage = (pnl_points / entry_price) * 100
        
        # Update trade record
        trade['status'] = 'closed'
        trade['exit_time'] = datetime.now()
        trade['exit_price'] = exit_price
        trade['pnl_points'] = pnl_points
        trade['pnl_percentage'] = pnl_percentage
        trade['close_reason'] = reason
        
        # Update daily limits if stop loss hit
        if "Stop" in reason and pnl_points < 0:
            daily_limits.stop_losses_hit += 1
        
        self.trade_history.append(trade)
        
        # Log trade closure
        logger.info(f"\n💰 BIG TREND TRADE CLOSED 💰")
        logger.info(f"Direction: {signal.signal}")
        logger.info(f"Entry: ${entry_price:,.2f}")
        logger.info(f"Exit: ${exit_price:,.2f}")
        logger.info(f"P&L: {pnl_points:+.0f} points ({pnl_percentage:+.2f}%)")
        logger.info(f"Reason: {reason}")
        logger.info(f"Daily SLs: {daily_limits.stop_losses_hit}/{daily_limits.max_stop_losses}")
        
        # Remove from active trades
        if trade in self.active_trades:
            self.active_trades.remove(trade)
    
    def get_big_trend_performance(self):
        """Get performance summary for big trend trading"""
        
        if not self.trade_history:
            return "No completed trades yet."
        
        total_trades = len(self.trade_history)
        winning_trades = len([t for t in self.trade_history if t['pnl_points'] > 0])
        big_winners = len([t for t in self.trade_history if t['pnl_points'] > 800])  # 800+ point moves
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        total_pnl = sum(t['pnl_points'] for t in self.trade_history)
        
        avg_win = np.mean([t['pnl_points'] for t in self.trade_history if t['pnl_points'] > 0]) if winning_trades > 0 else 0
        avg_loss = np.mean([t['pnl_points'] for t in self.trade_history if t['pnl_points'] < 0]) if (total_trades - winning_trades) > 0 else 0
        
        max_win = max([t['pnl_points'] for t in self.trade_history]) if self.trade_history else 0
        max_loss = min([t['pnl_points'] for t in self.trade_history]) if self.trade_history else 0
        
        summary = f"""
🎯 BIG TREND TRADING PERFORMANCE 🎯
===================================
Total Trades: {total_trades}
Winning Trades: {winning_trades} ({win_rate:.1%})
Big Winners (800+ pts): {big_winners}
Total P&L: {total_pnl:+.0f} points
Average Win: {avg_win:.0f} points
Average Loss: {avg_loss:.0f} points
Best Trade: {max_win:+.0f} points
Worst Trade: {max_loss:+.0f} points
===================================
        """
        
        return summary

# Example usage
if __name__ == "__main__":
    system = BigTrendTradingSystem()
    print("🚀 Big Trend Trading System Initialized!")
    print("Target: 800-3000+ point moves with 200-300 point stops")
    print("Strategy: Max 3 trades/day, Max 3 SLs/day, Trail only after 1:4 hit")
