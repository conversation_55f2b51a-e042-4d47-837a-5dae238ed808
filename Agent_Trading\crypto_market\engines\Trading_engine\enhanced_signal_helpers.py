"""
Enhanced Signal Helper Functions
===============================

Helper functions for the enhanced indicator system including:
- Confluence calculation
- Signal strength classification
- Dynamic risk allocation
- Trailing stop methods
- Confidence scoring
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
from enhanced_indicator import SignalStrength, TrailingMethod

def calculate_confluence(latest_3m, latest_15m, latest_1h) -> Dict:
    """Calculate multi-timeframe confluence"""

    buy_signals = []
    sell_signals = []
    timeframe_alignment = {'3m': False, '15m': False, '1h': False}

    # 3m timeframe signals
    tf_3m_signals = get_timeframe_signals(latest_3m, '3m')
    buy_signals.extend(tf_3m_signals['buy'])
    sell_signals.extend(tf_3m_signals['sell'])
    timeframe_alignment['3m'] = len(tf_3m_signals['buy']) > len(tf_3m_signals['sell'])

    # 15m timeframe signals
    tf_15m_signals = get_timeframe_signals(latest_15m, '15m')
    buy_signals.extend(tf_15m_signals['buy'])
    sell_signals.extend(tf_15m_signals['sell'])
    timeframe_alignment['15m'] = len(tf_15m_signals['buy']) > len(tf_15m_signals['sell'])

    # 1h timeframe signals
    tf_1h_signals = get_timeframe_signals(latest_1h, '1h')
    buy_signals.extend(tf_1h_signals['buy'])
    sell_signals.extend(tf_1h_signals['sell'])
    timeframe_alignment['1h'] = len(tf_1h_signals['buy']) > len(tf_1h_signals['sell'])

    # Calculate scores
    buy_score = len(buy_signals)
    sell_score = len(sell_signals)
    confluence_count = max(buy_score, sell_score)

    # Confluence details for logging
    confluence_details = {
        'buy_signals': buy_signals,
        'sell_signals': sell_signals,
        'timeframe_alignment': timeframe_alignment
    }

    return {
        'buy_score': buy_score,
        'sell_score': sell_score,
        'confluence_count': confluence_count,
        'timeframe_alignment': timeframe_alignment,
        'confluence_details': confluence_details
    }

def get_timeframe_signals(data, timeframe: str) -> Dict[str, List[str]]:
    """Get signals for a specific timeframe"""

    buy_signals = []
    sell_signals = []

    # MACD signals
    if hasattr(data, 'macd') and hasattr(data, 'macd_signal'):
        if data['macd'] > data['macd_signal'] and data['macd_hist'] > 0:
            buy_signals.append(f"{timeframe}_macd_bullish")
        elif data['macd'] < data['macd_signal'] and data['macd_hist'] < 0:
            sell_signals.append(f"{timeframe}_macd_bearish")

    # RSI signals
    if hasattr(data, 'rsi'):
        if data['rsi'] < 30:
            buy_signals.append(f"{timeframe}_rsi_oversold")
        elif data['rsi'] > 70:
            sell_signals.append(f"{timeframe}_rsi_overbought")

    # ADX trend strength
    if hasattr(data, 'adx') and hasattr(data, 'plus_di') and hasattr(data, 'minus_di'):
        if data['adx'] > 25:  # Strong trend
            if data['plus_di'] > data['minus_di']:
                buy_signals.append(f"{timeframe}_adx_bullish_trend")
            else:
                sell_signals.append(f"{timeframe}_adx_bearish_trend")

    # EMA alignment
    if hasattr(data, 'ema_9') and hasattr(data, 'ema_21'):
        if data['ema_9'] > data['ema_21'] and data['close'] > data['ema_9']:
            buy_signals.append(f"{timeframe}_ema_bullish")
        elif data['ema_9'] < data['ema_21'] and data['close'] < data['ema_9']:
            sell_signals.append(f"{timeframe}_ema_bearish")

    # Supertrend
    if hasattr(data, 'supertrend'):
        if data['supertrend'] == 1:
            buy_signals.append(f"{timeframe}_supertrend_bullish")
        elif data['supertrend'] == -1:
            sell_signals.append(f"{timeframe}_supertrend_bearish")

    # Support/Resistance
    if hasattr(data, 'dist_from_support') and hasattr(data, 'dist_from_resistance'):
        if data['dist_from_support'] < 0.02:  # Within 2% of support
            buy_signals.append(f"{timeframe}_near_support")
        elif data['dist_from_resistance'] < 0.02:  # Within 2% of resistance
            sell_signals.append(f"{timeframe}_near_resistance")

    # Volume confirmation
    if hasattr(data, 'volume') and hasattr(data, 'avg_volume'):
        if data['volume'] > data['avg_volume'] * 1.5:  # High volume
            buy_signals.append(f"{timeframe}_high_volume")

    return {'buy': buy_signals, 'sell': sell_signals}

def classify_signal_strength(confluence_signals: Dict, settings: Dict) -> SignalStrength:
    """Classify signal strength based on confluence"""

    confluence_count = confluence_signals['confluence_count']
    timeframe_alignment = confluence_signals['timeframe_alignment']

    # Count aligned timeframes
    aligned_timeframes = sum(timeframe_alignment.values())

    # Ultra high: 6+ confluence + all timeframes aligned
    if confluence_count >= 6 and aligned_timeframes == 3:
        return SignalStrength.ULTRA_HIGH

    # Strong: 5+ confluence + 2+ timeframes aligned
    elif confluence_count >= 5 and aligned_timeframes >= 2:
        return SignalStrength.STRONG

    # Moderate: 4+ confluence + 1+ timeframes aligned
    elif confluence_count >= 4 and aligned_timeframes >= 1:
        return SignalStrength.MODERATE

    # Weak: 3+ confluence
    else:
        return SignalStrength.WEAK

def calculate_stop_take_profit(
    latest_data,
    signal_direction: str,
    signal_strength: SignalStrength,
    settings: Dict
) -> Tuple[float, float]:
    """
    Calculate stop loss and take profit for BIG TREND CAPTURE
    - Stop Loss: Max 200-300 points
    - Take Profit: Minimum 1:4, Target 1:5, Big Trends 1:15
    """

    entry_price = latest_data['close']
    atr = latest_data.get('atr', entry_price * 0.02)  # Fallback to 2% if no ATR

    # Calculate stop loss in points (200-300 max)
    max_sl_points = settings.get('max_sl_points', 300)
    min_sl_points = settings.get('min_sl_points', 150)
    atr_sl_mult = settings.get('atr_sl_multiplier', 1.5)

    # ATR-based stop loss in points
    atr_sl_points = atr * atr_sl_mult

    # Ensure stop loss is within 150-300 point range
    sl_points = max(min_sl_points, min(atr_sl_points, max_sl_points))

    # Calculate take profit based on signal strength and trend potential
    min_rr = settings.get('min_rr', 4.0)  # Minimum 1:4
    target_rr = settings.get('target_rr', 5.0)  # Target 1:5
    big_trend_rr = settings.get('big_trend_rr', 15.0)  # Big trend 1:15

    # Take profit calculation based on signal strength
    if signal_strength == SignalStrength.ULTRA_HIGH:
        # Ultra high signals target big trends (800-3000+ points)
        tp_points = sl_points * big_trend_rr  # 1:15 ratio
    elif signal_strength == SignalStrength.STRONG:
        # Strong signals target good moves (1000-1500 points)
        tp_points = sl_points * (target_rr * 2)  # 1:10 ratio
    else:
        # Moderate signals target minimum (800-1200 points)
        tp_points = sl_points * target_rr  # 1:5 ratio

    # Apply direction
    if signal_direction == "BUY":
        stop_loss = entry_price - sl_points
        take_profit = entry_price + tp_points
    else:  # SELL
        stop_loss = entry_price + sl_points
        take_profit = entry_price - tp_points

    return stop_loss, take_profit

def calculate_dynamic_risk(
    signal_strength: SignalStrength,
    risk_reward_ratio: float,
    settings: Dict
) -> float:
    """Calculate dynamic risk allocation based on signal quality"""

    base_risk = settings.get('base_risk', 0.01)
    max_risk = settings.get('max_risk', 0.025)

    # Risk multipliers based on signal strength
    strength_multipliers = {
        SignalStrength.WEAK: 0.5,
        SignalStrength.MODERATE: 1.0,
        SignalStrength.STRONG: 1.3,
        SignalStrength.ULTRA_HIGH: 1.5
    }

    # R:R bonus (higher R:R = higher risk allocation)
    rr_bonus = min(1.2, 1.0 + (risk_reward_ratio - 2.0) * 0.1)

    # Calculate final risk allocation
    risk_allocation = base_risk * strength_multipliers[signal_strength] * rr_bonus

    # Cap at maximum risk
    return min(risk_allocation, max_risk)

def determine_trailing_method(
    signal_strength: SignalStrength,
    confluence_signals: Dict
) -> TrailingMethod:
    """Determine optimal trailing stop method"""

    # Ultra high strength signals use dynamic trailing
    if signal_strength == SignalStrength.ULTRA_HIGH:
        return TrailingMethod.DYNAMIC

    # Strong signals with trend confirmation use structure-based
    elif signal_strength == SignalStrength.STRONG:
        # Check if we have trend signals
        details = confluence_signals.get('confluence_details', {})
        buy_signals = details.get('buy_signals', [])
        sell_signals = details.get('sell_signals', [])

        trend_signals = [s for s in (buy_signals + sell_signals) if 'adx' in s or 'supertrend' in s]

        if trend_signals:
            return TrailingMethod.STRUCTURE_BASED
        else:
            return TrailingMethod.ATR_BASED

    # Default to ATR-based for moderate/weak signals
    else:
        return TrailingMethod.ATR_BASED

def calculate_confidence_score(
    confluence_signals: Dict,
    risk_reward_ratio: float,
    signal_strength: SignalStrength
) -> float:
    """Calculate overall confidence score (0-100)"""

    # Base confidence from confluence count
    confluence_score = min(40, confluence_signals['confluence_count'] * 6)

    # Timeframe alignment bonus
    aligned_timeframes = sum(confluence_signals['timeframe_alignment'].values())
    alignment_score = aligned_timeframes * 15

    # Risk-reward bonus
    rr_score = min(25, (risk_reward_ratio - 1.0) * 5)

    # Signal strength bonus
    strength_bonus = {
        SignalStrength.WEAK: 0,
        SignalStrength.MODERATE: 5,
        SignalStrength.STRONG: 10,
        SignalStrength.ULTRA_HIGH: 20
    }

    total_confidence = confluence_score + alignment_score + rr_score + strength_bonus[signal_strength]

    return min(100, max(0, total_confidence))
