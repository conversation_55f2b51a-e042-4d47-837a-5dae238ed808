"""
Run Big Trend Strategy Backtest
===============================

Simple script to run VectorBT backtest on your 286K records database.
This will test your big trend strategy on real BTC data.
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# Add project paths
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backtester_vbt import run_big_trend_backtest
from data_loader import TradingDataLoader, load_backtest_data, get_data_summary

def run_sample_backtest():
    """Run a sample backtest on recent data"""

    print("🚀 BIG TREND STRATEGY BACKTEST")
    print("=" * 50)

    # Load recent 30 days of data using the data loader
    try:
        loader = TradingDataLoader()
        df_3m, df_15m, df_1h = loader.load_recent_data(days=30)
    except Exception as e:
        print(f"❌ Failed to load data: {e}")
        return

    print(f"   Test period: {df_3m.index[0]} to {df_3m.index[-1]}")
    print(f"   3m candles: {len(df_3m):,}")
    print(f"   15m candles: {len(df_15m):,}")
    print(f"   1h candles: {len(df_1h):,}")

    # Run backtest with $100,000 starting capital
    results = run_big_trend_backtest(
        df_3m=df_3m,
        df_15m=df_15m,
        df_1h=df_1h,
        initial_capital=100000
    )

    if results:
        print("\n🎯 BACKTEST COMPLETED SUCCESSFULLY!")

        # Additional analysis
        pf = results['portfolio']

        # Plot results (if you have matplotlib)
        try:
            import matplotlib.pyplot as plt

            print("\n📈 Generating performance charts...")

            # Create subplots
            fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))

            # Price and trades
            ax1.plot(df_3m.index, df_3m['close'], label='BTC Price', alpha=0.7)

            # Mark entry points
            entries = pf.trades.entry_idx
            entry_prices = pf.trades.entry_price

            if len(entries) > 0:
                ax1.scatter(df_3m.index[entries], entry_prices,
                           color='green', marker='^', s=100, label='Entries', zorder=5)

            ax1.set_title('BTC Price with Trade Entries')
            ax1.set_ylabel('Price ($)')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # Portfolio value
            portfolio_value = pf.value()
            ax2.plot(portfolio_value.index, portfolio_value.values,
                    label='Portfolio Value', color='blue', linewidth=2)
            ax2.set_title('Portfolio Value Over Time')
            ax2.set_ylabel('Value ($)')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # Drawdown
            drawdown = pf.drawdown()
            ax3.fill_between(drawdown.index, drawdown.values, 0,
                           color='red', alpha=0.3, label='Drawdown')
            ax3.set_title('Portfolio Drawdown')
            ax3.set_ylabel('Drawdown (%)')
            ax3.set_xlabel('Date')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig('big_trend_backtest_results.png', dpi=300, bbox_inches='tight')
            print("   Charts saved as 'big_trend_backtest_results.png'")

        except ImportError:
            print("   (Install matplotlib for charts: pip install matplotlib)")

        return results

    else:
        print("❌ Backtest failed")
        return None

def run_full_backtest():
    """Run backtest on full dataset"""

    print("🚀 FULL DATASET BACKTEST")
    print("=" * 50)
    print("⚠️  This will take longer but test the full strategy")

    # Load all data using the data loader
    try:
        loader = TradingDataLoader()
        df_3m, df_15m, df_1h = loader.load_backtest_data()
    except Exception as e:
        print(f"❌ Failed to load data: {e}")
        return

    # Run on full dataset
    results = run_big_trend_backtest(
        df_3m=df_3m,
        df_15m=df_15m,
        df_1h=df_1h,
        initial_capital=100000
    )

    return results

def main():
    """Main function"""

    print("🎯 BIG TREND STRATEGY BACKTESTING")
    print("=" * 50)
    print("Choose option:")
    print("1. Sample backtest (last 30 days) - Fast")
    print("2. Full backtest (all data) - Comprehensive")
    print("3. Data summary - Check available data")

    choice = input("\nEnter choice (1, 2, or 3): ").strip()

    if choice == "1":
        results = run_sample_backtest()
    elif choice == "2":
        results = run_full_backtest()
    elif choice == "3":
        # Show data summary
        try:
            loader = TradingDataLoader()
            summary = loader.get_data_summary()
            print("\n🎯 Ready for backtesting!")
        except Exception as e:
            print(f"❌ Error getting data summary: {e}")
        return
    else:
        print("Invalid choice")
        return

    if results:
        print(f"\n🎉 BACKTEST SUMMARY:")
        print(f"   Total Return: {results['total_return']:.2%}")
        print(f"   Win Rate: {results['win_rate']:.2%}")
        print(f"   Total Trades: {results['total_trades']}")
        print(f"   Big Winners: {results['big_winners']}")
        print(f"   Max Drawdown: {results['max_drawdown']:.2%}")

        # Save results
        import json
        with open('backtest_results.json', 'w') as f:
            # Convert numpy types to native Python types for JSON serialization
            json_results = {}
            for key, value in results.items():
                if key != 'portfolio':  # Skip the portfolio object
                    if hasattr(value, 'item'):  # numpy scalar
                        json_results[key] = value.item()
                    elif isinstance(value, dict):
                        json_results[key] = value
                    else:
                        json_results[key] = value

            json.dump(json_results, f, indent=2)

        print(f"\n💾 Results saved to 'backtest_results.json'")

if __name__ == "__main__":
    main()
