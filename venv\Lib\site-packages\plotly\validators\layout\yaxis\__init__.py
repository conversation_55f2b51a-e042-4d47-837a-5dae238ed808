import sys
from _plotly_utils.importers import relative_import

__all__, __getattr__, __dir__ = relative_import(
    __name__,
    [],
    [
        "._zerolinewidth.ZerolinewidthValidator",
        "._zerolinecolor.ZerolinecolorValidator",
        "._zeroline.ZerolineValidator",
        "._visible.VisibleValidator",
        "._uirevision.UirevisionValidator",
        "._type.TypeValidator",
        "._title.TitleValidator",
        "._tickwidth.TickwidthValidator",
        "._tickvalssrc.TickvalssrcValidator",
        "._tickvals.TickvalsValidator",
        "._ticktextsrc.TicktextsrcValidator",
        "._ticktext.TicktextValidator",
        "._ticksuffix.TicksuffixValidator",
        "._tickson.TicksonValidator",
        "._ticks.TicksValidator",
        "._tickprefix.TickprefixValidator",
        "._tickmode.TickmodeValidator",
        "._ticklen.TicklenValidator",
        "._ticklabelstep.TicklabelstepValidator",
        "._ticklabelstandoff.TicklabelstandoffValidator",
        "._ticklabelshift.TicklabelshiftValidator",
        "._ticklabelposition.TicklabelpositionValidator",
        "._ticklabeloverflow.TicklabeloverflowValidator",
        "._ticklabelmode.TicklabelmodeValidator",
        "._ticklabelindexsrc.TicklabelindexsrcValidator",
        "._ticklabelindex.TicklabelindexValidator",
        "._tickformatstopdefaults.TickformatstopdefaultsValidator",
        "._tickformatstops.TickformatstopsValidator",
        "._tickformat.TickformatValidator",
        "._tickfont.TickfontValidator",
        "._tickcolor.TickcolorValidator",
        "._tickangle.TickangleValidator",
        "._tick0.Tick0Validator",
        "._spikethickness.SpikethicknessValidator",
        "._spikesnap.SpikesnapValidator",
        "._spikemode.SpikemodeValidator",
        "._spikedash.SpikedashValidator",
        "._spikecolor.SpikecolorValidator",
        "._side.SideValidator",
        "._showticksuffix.ShowticksuffixValidator",
        "._showtickprefix.ShowtickprefixValidator",
        "._showticklabels.ShowticklabelsValidator",
        "._showspikes.ShowspikesValidator",
        "._showline.ShowlineValidator",
        "._showgrid.ShowgridValidator",
        "._showexponent.ShowexponentValidator",
        "._showdividers.ShowdividersValidator",
        "._shift.ShiftValidator",
        "._separatethousands.SeparatethousandsValidator",
        "._scaleratio.ScaleratioValidator",
        "._scaleanchor.ScaleanchorValidator",
        "._rangemode.RangemodeValidator",
        "._rangebreakdefaults.RangebreakdefaultsValidator",
        "._rangebreaks.RangebreaksValidator",
        "._range.RangeValidator",
        "._position.PositionValidator",
        "._overlaying.OverlayingValidator",
        "._nticks.NticksValidator",
        "._mirror.MirrorValidator",
        "._minor.MinorValidator",
        "._minexponent.MinexponentValidator",
        "._minallowed.MinallowedValidator",
        "._maxallowed.MaxallowedValidator",
        "._matches.MatchesValidator",
        "._linewidth.LinewidthValidator",
        "._linecolor.LinecolorValidator",
        "._layer.LayerValidator",
        "._labelalias.LabelaliasValidator",
        "._insiderange.InsiderangeValidator",
        "._hoverformat.HoverformatValidator",
        "._gridwidth.GridwidthValidator",
        "._griddash.GriddashValidator",
        "._gridcolor.GridcolorValidator",
        "._fixedrange.FixedrangeValidator",
        "._exponentformat.ExponentformatValidator",
        "._dtick.DtickValidator",
        "._domain.DomainValidator",
        "._dividerwidth.DividerwidthValidator",
        "._dividercolor.DividercolorValidator",
        "._constraintoward.ConstraintowardValidator",
        "._constrain.ConstrainValidator",
        "._color.ColorValidator",
        "._categoryorder.CategoryorderValidator",
        "._categoryarraysrc.CategoryarraysrcValidator",
        "._categoryarray.CategoryarrayValidator",
        "._calendar.CalendarValidator",
        "._autotypenumbers.AutotypenumbersValidator",
        "._autotickangles.AutotickanglesValidator",
        "._autoshift.AutoshiftValidator",
        "._autorangeoptions.AutorangeoptionsValidator",
        "._autorange.AutorangeValidator",
        "._automargin.AutomarginValidator",
        "._anchor.AnchorValidator",
    ],
)
